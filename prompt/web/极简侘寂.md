## 系统提示词

基于提供的文本或者网站等关键信息，帮我用类似苹果发布会 PPT 的 Hybrid Grid 风格的视觉设计结合孟菲斯设计色系生成一个中文动态网页展示，具体要求为：

1. 尽量在一页展示全部信息
   核心背景：【源于自然的、未经雕琢的素色（如枯寂的灰白、风化的米色、褪色的亚麻色、沉静的陶土色、或带有斑驳痕迹的微水泥色），强调材质本身的肌理与不完美感（如粗糙的质地、自然的纹路、或时间的印记），营造宁静、返璞归真、接纳无常的氛围】。
   文字与常规UI颜色：采用【与背景相融或形成微妙对比的暗哑色调（如深灰、碳色、褪色的墨黑、或从背景材质中提取的更深层次的自然色），追求视觉上的平和与不突兀】，避免使用任何鲜艳、光鲜或具有人工感的色彩，保持整体的质朴与内敛。
2. 强调超大字体或数字突出核心要点，画面中有超大视觉元素强调重点，与小元素的比例形成反差
3. 网页需要以响应式兼容更大的显示器宽度比如1920px及以上
4. 中英文混用，中文大字体粗体，英文小字作为点缀
5. 简洁的勾线图形化作为数据可视化或者配图元素
6. 运用高亮色自身透明度渐变制造科技感，但是不同高亮色不要互相渐变
7. 数据可以引用在线的图表组件，样式需要跟主题一致
8. 使用HTML5、TailwindCSS 3.0+（通过CDN引入）和必要的JavaScript
9. 使用专业图标库如Font Awesome或Material Icons（通过CDN引入）
10. 避免使用emoji作为主要图标
11. 不要省略内容要点
12. 过程输出也采用简体中文

## 样例输入

诋毁，本身就是一种仰望。

## 样例输出

这又是一句非常凝练且富有洞察力的短语！“诋毁” 与 “仰望” 之间的张力是核心。

我会采用极简但极具冲击力的设计，让文字本身成为画面的绝对焦点。

**设计核心：**

*   **对比：** "诋毁" 和 "仰望" 在视觉上形成对比，可能通过大小、颜色或高亮效果。
*   **连接：** "本身就是一种" 作为桥梁，视觉上连接两者。
*   **意境：** 整体营造一种沉思、揭示真相的氛围。

```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>诋毁与仰望</title>
    <script src="https://cdn.tailwindcss.com?plugins=typography"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;700;900&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Noto Sans SC', 'Helvetica Neue', Arial, sans-serif;
            background-color: #DCD6CC; /* 褪色的亚麻色 */
            /* Slightly more textured background */
            background-image: linear-gradient(45deg, rgba(0,0,0,0.01) 25%, transparent 25%, transparent 75%, rgba(0,0,0,0.01) 75%, rgba(0,0,0,0.01)),
                              linear-gradient(45deg, rgba(0,0,0,0.01) 25%, transparent 25%, transparent 75%, rgba(0,0,0,0.01) 75%, rgba(0,0,0,0.01));
            background-size: 30px 30px;
            background-position: 0 0, 15px 15px;
            color: #4A3B31; /* 更深的陶土色，近乎褪色的墨黑 */
            overflow: hidden;
            display: flex;
            flex-direction: column;
            min-height: 100vh;
            align-items: center;
            justify-content: center;
            text-align: center;
        }
        .font-display {
            font-weight: 900;
            line-height: 1.05; /* Adjusted for stacking */
            letter-spacing: -0.03em;
        }
        .text-highlight-gradient {
            background-image: linear-gradient(to bottom, rgba(104, 144, 148, 0.8) 0%, rgba(104, 144, 148, 0.4) 70%, rgba(104, 144, 148, 0.15) 100%);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            padding-bottom: 0.1em; /* Ensure gradient covers descenders */
        }
        .text-connecting {
            font-size: clamp(1.5rem, 5vw, 3.5rem); /* Responsive size for connector */
            font-weight: 400; /* Lighter weight */
            color: #6B5D55; /* Softer, related to main text color */
            display: block; /* Ensure it's on its own line */
            margin: 0.3em 0; /* Vertical spacing */
            letter-spacing: normal;
        }
        .text-colossus {
            font-size: clamp(4rem, 18vw, 15rem); /* Even larger for key words */
        }

        .memphis-accent {
            position: absolute;
            opacity: 0.05;
            pointer-events: none;
            z-index: 0;
        }

        @keyframes slowDrift {
            0%, 100% { transform: translate(0, 0) rotate(0deg); }
            25% { transform: translate(5px, -8px) rotate(1deg); }
            75% { transform: translate(-5px, 8px) rotate(-1deg); }
        }
        .animate-slow-drift {
            animation: slowDrift 15s ease-in-out infinite;
        }
    </style>
</head>
<body class="p-4 md:p-6 selection:bg-[#689094]/30 selection:text-[#4A3B31]">

    <!-- Subtle Memphis Accents -->
    <div class="memphis-accent w-40 h-1 bg-[#B08D57] top-[15%] left-[10%] transform -rotate-[30deg] animate-slow-drift" style="animation-delay: -2s;"></div>
    <div class="memphis-accent w-2 h-32 bg-[#C8BFB6] bottom-[20%] right-[12%] transform rotate-[20deg] animate-slow-drift" style="animation-delay: -5s;"></div>
    <div class="memphis-accent w-24 h-24 border-2 border-[#689094] rounded-full top-[50%] left-[50%] transform -translate-x-1/2 -translate-y-1/2 opacity-[0.03]"></div>


    <main class="relative z-10">
        <h1 class="font-display text-colossus text-[#3D3D3D]">
            诋毁
        </h1>
        <span class="text-connecting">本身就是一种</span>
        <h2 class="font-display text-colossus">
            <span class="text-highlight-gradient">仰望</span>
        </h2>

        <p class="mt-10 md:mt-16 text-base md:text-lg text-[#706158] uppercase tracking-wider">
            SLANDER IS ITSELF A FORM OF ADMIRATION
        </p>

        <!-- A simple line art element as a divider/focus point -->
        <div class="mx-auto mt-8 md:mt-12 w-1/3 max-w-[100px] h-0.5 bg-[#B08D57] opacity-40"></div>

    </main>

    <footer class="absolute bottom-4 md:bottom-6 text-center w-full z-10">
        <p class="text-xs text-[#84756C] opacity-80">An insight into perception.</p>
    </footer>

</body>
</html>
```

**设计解析:**

1.  **Background & Palette:**
	*   `background-color: #DCD6CC;` (褪色的亚麻色) is used.
	*   A very subtle `background-image` linear gradient pattern is added to give a slight "斑驳痕迹" (mottled) or "粗糙质地" (rough texture) feel without being distracting. This is a common Wabi-Sabi technique.
	*   Text colors are deep and muted (`#4A3B31`, `#3D3D3D`, `#6B5D55`).
2.  **Typography is Key:**
	*   **"诋毁" (Slander):** Uses `text-colossus` (`clamp(4rem, 18vw, 15rem)`) for maximum impact, styled with a dark, solid Wabi-Sabi color (`#3D3D3D`). It's the first impactful word.
	*   **"本身就是一种" (is itself a form of):** This connecting phrase uses a custom class `text-connecting`.
		*   `font-size: clamp(1.5rem, 5vw, 3.5rem);` makes it significantly smaller than the keywords but still substantial and responsive.
		*   `font-weight: 400;` (normal weight) to recede visually.
		*   `color: #6B5D55;` a softer, related Wabi-Sabi tone.
		*   `display: block; margin: 0.3em 0;` ensures it sits on its own line between the two keywords with appropriate spacing.
	*   **"仰望" (Admiration):** Also uses `text-colossus`. The `text-highlight-gradient` is applied here, making it the visual culmination and giving it a sense of elevation or enlightenment, which aligns with "仰望". The gradient is `to bottom` for a subtle dripping/glowing effect.
3.  **Layout & Flow:** The three parts are stacked vertically, creating a clear reading path: Slander -> is a form of -> Admiration. This structure emphasizes the paradoxical transformation.
4.  **Minimalism:** The page is extremely focused. No distracting large icons. The English translation is small and serves as a subtitle.
5.  **Memphis Accents:** Very few and very subtle (`opacity: 0.05` or `0.03`). Simple lines or a faint circle, animated with `slowDrift` to add a hint of life without pulling focus from the text. Their colors are from the Wabi-Sabi palette.
6.  **Line Art Element:** A simple horizontal line (`<div class="mx-auto mt-8 md:mt-12 w-1/3 max-w-[100px] h-0.5 bg-[#B08D57] opacity-40"></div>`) acts as a subtle "勾线图形" (line art graphic), grounding the text block or acting as a visual pause.
7.  **Footer:** Extremely minimal, as requested.

This design aims for a powerful, contemplative statement page where the inherent paradox of the quote is visually represented through scale, color, and subtle emphasis.