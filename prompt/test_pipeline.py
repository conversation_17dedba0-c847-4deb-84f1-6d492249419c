#!/usr/bin/env python3
"""
Prompt Pipeline 测试脚本

这个脚本用于测试 prompt_pipeline.py 的基本功能，包括：
- 命令解析
- prompt文件查找
- 格式化输出

使用方法：
python prompt/test_pipeline.py
"""

import sys
from pathlib import Path
from prompt_pipeline import PromptPipeline


def test_command_parsing():
    """测试命令解析功能"""
    print("🧪 测试命令解析功能...")
    
    pipeline = PromptPipeline()
    
    # 测试用例
    test_cases = [
        ("简单文本 |> 文章改写", ("简单文本", ["文章改写"])),
        ("复杂文本 |> 文章改写 |> 极简侘寂", ("复杂文本", ["文章改写", "极简侘寂"])),
        ("文本 |> 文章改写 |> 保存文件", ("文本", ["文章改写", "保存文件"])),
        ("多行\n文本内容 |> rewrite/文章改写 |> web/极简侘寂 |> 保存", ("多行\n文本内容", ["rewrite/文章改写", "web/极简侘寂", "保存"])),
    ]
    
    for i, (command, expected) in enumerate(test_cases, 1):
        try:
            result = pipeline.parse_pipeline_command(command)
            if result == expected:
                print(f"  ✅ 测试 {i}: 通过")
            else:
                print(f"  ❌ 测试 {i}: 失败")
                print(f"     期望: {expected}")
                print(f"     实际: {result}")
        except Exception as e:
            print(f"  ❌ 测试 {i}: 异常 - {e}")
    
    print()


def test_prompt_file_finding():
    """测试prompt文件查找功能"""
    print("🔍 测试prompt文件查找功能...")
    
    pipeline = PromptPipeline()
    
    # 测试用例
    test_cases = [
        "文章改写",  # 应该找到 rewrite/文章改写.md
        "rewrite/文章改写",  # 完整路径
        "极简侘寂",  # 应该找到 web/极简侘寂.md
        "web/极简侘寂",  # 完整路径
        "不存在的prompt",  # 应该返回None
    ]
    
    for prompt_name in test_cases:
        result = pipeline.find_prompt_file(prompt_name)
        if result:
            relative_path = result.relative_to(pipeline.prompt_dir)
            print(f"  ✅ '{prompt_name}' -> {relative_path}")
        else:
            print(f"  ❌ '{prompt_name}' -> 未找到")
    
    print()


def test_ai_request_formatting():
    """测试AI请求格式化功能"""
    print("📝 测试AI请求格式化功能...")
    
    pipeline = PromptPipeline()
    
    prompt_content = "这是一个测试prompt内容"
    input_text = "这是测试输入文本"
    
    formatted_request = pipeline.format_ai_request(prompt_content, input_text, 1, 2)
    
    # 检查格式化结果是否包含必要的元素
    required_elements = [
        "PROMPT PIPELINE",
        "步骤 1/2",
        "PROMPT 内容",
        prompt_content,
        "输入文本",
        input_text,
        "请根据上述prompt处理输入文本"
    ]
    
    all_present = True
    for element in required_elements:
        if element not in formatted_request:
            print(f"  ❌ 缺少元素: '{element}'")
            all_present = False
    
    if all_present:
        print("  ✅ AI请求格式化正确")
        print("  📄 格式化结果预览:")
        print("  " + "─" * 50)
        # 显示前几行作为预览
        lines = formatted_request.split('\n')[:10]
        for line in lines:
            print(f"  {line}")
        if len(formatted_request.split('\n')) > 10:
            print("  ...")
    
    print()


def test_file_operations():
    """测试文件操作功能"""
    print("💾 测试文件操作功能...")
    
    pipeline = PromptPipeline()
    
    # 测试保存功能（使用测试内容）
    test_content = "这是测试输出内容\n包含多行文本"
    test_prompt_chain = ["文章改写", "极简侘寂"]
    
    try:
        output_path = pipeline.save_output(test_content, test_prompt_chain)
        
        if output_path.exists():
            print(f"  ✅ 文件保存成功: {output_path.relative_to(pipeline.prompt_dir)}")
            
            # 验证文件内容
            saved_content = output_path.read_text(encoding='utf-8')
            if saved_content == test_content:
                print("  ✅ 文件内容正确")
            else:
                print("  ❌ 文件内容不匹配")
            
            # 清理测试文件
            output_path.unlink()
            print("  🧹 测试文件已清理")
        else:
            print("  ❌ 文件保存失败")
    
    except Exception as e:
        print(f"  ❌ 文件操作异常: {e}")
    
    print()


def test_error_handling():
    """测试错误处理"""
    print("⚠️ 测试错误处理...")
    
    pipeline = PromptPipeline()
    
    # 测试无效命令
    try:
        pipeline.parse_pipeline_command("无效命令")
        print("  ❌ 应该抛出异常但没有")
    except ValueError:
        print("  ✅ 正确处理无效命令")
    except Exception as e:
        print(f"  ⚠️ 意外异常类型: {e}")
    
    # 测试不存在的prompt文件
    result = pipeline.find_prompt_file("绝对不存在的prompt")
    if result is None:
        print("  ✅ 正确处理不存在的prompt")
    else:
        print("  ❌ 应该返回None但返回了文件路径")
    
    print()


def main():
    """运行所有测试"""
    print("🚀 开始 Prompt Pipeline 功能测试")
    print("=" * 60)
    
    test_command_parsing()
    test_prompt_file_finding()
    test_ai_request_formatting()
    test_file_operations()
    test_error_handling()
    
    print("=" * 60)
    print("🎉 测试完成！")
    print()
    print("💡 提示：如果所有测试都通过，你可以开始使用 prompt_pipeline.py 了！")
    print("📖 查看 USAGE_EXAMPLES.md 了解详细使用方法。")


if __name__ == "__main__":
    main()
