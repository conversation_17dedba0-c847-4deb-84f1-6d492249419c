#!/usr/bin/env python3
"""
PP - Prompt Pipeline

一个基于文件系统的、可扩展的 Prompt 工作流引擎。
支持会话模式和交互模式两种使用方式。

使用方法：
  python pp.py "输入文字 |> prompt1 |> prompt2 |> ..."
  python pp.py "输入文字 |> prompt1" --interactive
  python pp.py --list
"""

import sys
import os
import re
from pathlib import Path
from datetime import datetime
from typing import List, Tuple, Optional
import argparse


class PromptPipeline:
    def __init__(self, prompt_dir: Path = None):
        """初始化Prompt Pipeline"""
        if prompt_dir is None:
            self.prompt_dir = Path(__file__).parent.resolve()
        else:
            self.prompt_dir = Path(prompt_dir).resolve()
        
        self.output_dir = self.prompt_dir / 'output'
        
    def parse_pipeline_command(self, command: str) -> Tuple[str, List[str]]:
        """解析管道命令"""
        parts = [part.strip() for part in command.split('|>')]
        
        if len(parts) < 2:
            raise ValueError("命令格式错误。正确格式：'输入文字 |> prompt1 |> prompt2 |> ...'")
        
        input_text = parts[0]
        prompt_steps = parts[1:]
        
        return input_text, prompt_steps
    
    def find_prompt_file(self, prompt_name: str) -> Optional[Path]:
        """智能查找prompt文件"""
        # 如果包含路径分隔符，直接使用相对路径
        if '/' in prompt_name or '\\' in prompt_name:
            prompt_path = self.prompt_dir / f"{prompt_name}.md"
            if prompt_path.exists():
                return prompt_path
        
        # 在所有子目录中搜索匹配的文件
        for md_file in self.prompt_dir.rglob("*.md"):
            # 跳过README.md、USAGE_EXAMPLES.md和output目录
            if (md_file.name in ["README.md", "USAGE_EXAMPLES.md"] or
                "output" in md_file.parts):
                continue
                
            # 检查文件名是否匹配（不包含扩展名）
            if md_file.stem == prompt_name:
                return md_file
                
            # 检查完整相对路径是否匹配
            relative_path = md_file.relative_to(self.prompt_dir)
            relative_path_str = str(relative_path).replace('.md', '')
            if relative_path_str == prompt_name:
                return md_file
        
        return None
    
    def load_prompt_content(self, prompt_file: Path) -> str:
        """加载prompt文件内容"""
        try:
            return prompt_file.read_text(encoding='utf-8')
        except Exception as e:
            raise RuntimeError(f"无法读取prompt文件 {prompt_file}: {e}")
    
    def save_output(self, content: str, prompt_chain: List[str]) -> Path:
        """保存输出内容到文件"""
        # 创建今天的输出目录
        today = datetime.now().strftime("%Y-%m-%d")
        today_dir = self.output_dir / today
        today_dir.mkdir(parents=True, exist_ok=True)
        
        # 生成文件名
        timestamp = datetime.now().strftime("%H%M%S")
        last_prompt = prompt_chain[-1] if prompt_chain else "output"
        # 清理文件名中的特殊字符
        clean_prompt_name = re.sub(r'[^\w\-_\u4e00-\u9fff]', '_', last_prompt)
        filename = f"{timestamp}_{clean_prompt_name}.md"
        
        # 保存文件
        output_path = today_dir / filename
        output_path.write_text(content, encoding='utf-8')
        
        return output_path
    
    def session_mode(self, command: str) -> str:
        """
        会话模式：生成格式化的会话请求，适合在AI会话中使用
        """
        try:
            # 解析命令
            input_text, prompt_steps = self.parse_pipeline_command(command)
            
            # 检查保存指令（默认保存，除非明确指定不保存）
            save_file = True
            actual_prompts = []

            for step in prompt_steps:
                if step.lower() in ['不保存', '不保存文件', 'no save', 'nosave']:
                    save_file = False
                elif step.lower() in ['保存文件', '保存', 'save', 'save file']:
                    save_file = True  # 显式保存（虽然默认就是True）
                else:
                    actual_prompts.append(step)
            
            if not actual_prompts:
                return "❌ 错误：没有找到有效的prompt步骤"
            
            # 生成会话请求
            separator = "=" * 60
            request_parts = []
            
            # 添加标题
            request_parts.append(f"""
{separator}
🚀 PROMPT PIPELINE 会话请求
{separator}

📊 **输入文本**：{input_text}
🔗 **处理链**：{' → '.join(actual_prompts)}
💾 **保存文件**：{'是' if save_file else '否'}

{separator}
""")
            
            # 为每个prompt生成处理步骤
            current_input = input_text
            
            for i, prompt_name in enumerate(actual_prompts, 1):
                # 查找prompt文件
                prompt_file = self.find_prompt_file(prompt_name)
                if not prompt_file:
                    return f"❌ 错误：找不到prompt文件 '{prompt_name}'"
                
                # 加载prompt内容
                prompt_content = self.load_prompt_content(prompt_file)
                
                # 生成步骤请求
                step_request = f"""
### 🔄 步骤 {i}/{len(actual_prompts)}: {prompt_name}

**Prompt文件**: `{prompt_file.relative_to(self.prompt_dir)}`

**Prompt内容**:
```
{prompt_content}
```

**当前输入**:
```
{current_input}
```

**请求**: 请根据上述prompt处理当前输入，直接输出处理结果。

---
"""
                request_parts.append(step_request)
                
                # 为下一步准备（如果有多个步骤）
                if i < len(actual_prompts):
                    current_input = f"[步骤{i}的输出结果]"
            
            # 添加保存说明
            if save_file:
                request_parts.append(f"""
### 💾 保存说明

处理完成后，最终结果将保存到：
- 目录：`prompt/output/{datetime.now().strftime('%Y-%m-%d')}/`
- 文件名：`HHMMSS_{actual_prompts[-1]}.md`

{separator}
""")
            
            return ''.join(request_parts)
            
        except Exception as e:
            return f"❌ 生成请求出错：{e}"
    

    
    def list_prompts(self) -> str:
        """列出所有可用的prompt"""
        result = ["📋 可用的Prompt列表：", "-" * 40]
        
        for md_file in self.prompt_dir.rglob("*.md"):
            if (md_file.name in ["README.md", "USAGE_EXAMPLES.md"] or
                "output" in md_file.parts):
                continue
            relative_path = md_file.relative_to(self.prompt_dir)
            prompt_name = str(relative_path).replace('.md', '')
            result.append(f"  • {prompt_name}")
            result.append(f"    文件：{relative_path}")
            result.append("")
        
        return '\n'.join(result)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="PP - Prompt Pipeline 工作流引擎",
        epilog="示例: python pp.py \"你的文字 |> 文章改写 |> 极简侘寂 |> 保存文件\"",
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    parser.add_argument(
        "command",
        help="管道命令，格式：'输入文字 |> prompt1 |> prompt2 |> ...'",
        nargs='?'
    )

    parser.add_argument(
        "--list", "-l",
        action="store_true",
        help="列出所有可用的prompt"
    )
    
    args = parser.parse_args()
    
    pipeline = PromptPipeline()
    
    if args.list:
        print(pipeline.list_prompts())
        return
    
    if not args.command:
        print("❌ 错误：请提供管道命令")
        print("\n使用方法：")
        print("  python pp.py \"输入文字 |> prompt1 |> prompt2\"")
        print("  python pp.py --list  # 查看可用prompt")
        print("  python pp.py --help  # 查看帮助")
        return
    
    result = pipeline.session_mode(args.command)
    
    print(result)


if __name__ == "__main__":
    main()
