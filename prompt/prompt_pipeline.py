#!/usr/bin/env python3
"""
Prompt Pipeline - 会话内Prompt工作流引擎

这个脚本实现了在当前大模型会话中串联调用多个prompt的功能。
支持管道式语法，如：'输入文字' |> 文章改写 |> 极简侘寂 |> 保存文件

使用方法：
python prompt/prompt_pipeline.py "你的输入文字 |> 文章改写 |> 极简侘寂 |> 保存文件"
"""

import sys
import os
import re
from pathlib import Path
from datetime import datetime
from typing import List, Tuple, Optional
import argparse


class PromptPipeline:
    def __init__(self, prompt_dir: Path = None):
        """初始化Prompt管道处理器"""
        if prompt_dir is None:
            self.prompt_dir = Path(__file__).parent.resolve()
        else:
            self.prompt_dir = Path(prompt_dir).resolve()
        
        self.output_dir = self.prompt_dir / 'output'
        
    def parse_pipeline_command(self, command: str) -> <PERSON><PERSON>[str, List[str]]:
        """
        解析管道命令
        
        Args:
            command: 管道命令字符串，如 "输入文字 |> 文章改写 |> 极简侘寂 |> 保存文件"
            
        Returns:
            (input_text, prompt_steps): 输入文本和prompt步骤列表
        """
        # 使用 |> 分割命令
        parts = [part.strip() for part in command.split('|>')]
        
        if len(parts) < 2:
            raise ValueError("命令格式错误。正确格式：'输入文字 |> prompt1 |> prompt2 |> ...'")
        
        input_text = parts[0]
        prompt_steps = parts[1:]
        
        return input_text, prompt_steps
    
    def find_prompt_file(self, prompt_name: str) -> Optional[Path]:
        """
        智能查找prompt文件
        
        Args:
            prompt_name: prompt名称，如 "文章改写" 或 "rewrite/文章改写"
            
        Returns:
            prompt文件路径，如果找不到返回None
        """
        # 如果包含路径分隔符，直接使用相对路径
        if '/' in prompt_name or '\\' in prompt_name:
            prompt_path = self.prompt_dir / f"{prompt_name}.md"
            if prompt_path.exists():
                return prompt_path
        
        # 在所有子目录中搜索匹配的文件
        for md_file in self.prompt_dir.rglob("*.md"):
            # 跳过README.md和output目录
            if md_file.name == "README.md" or "output" in md_file.parts:
                continue
                
            # 检查文件名是否匹配（不包含扩展名）
            if md_file.stem == prompt_name:
                return md_file
                
            # 检查完整相对路径是否匹配
            relative_path = md_file.relative_to(self.prompt_dir)
            relative_path_str = str(relative_path).replace('.md', '')
            if relative_path_str == prompt_name:
                return md_file
        
        return None
    
    def load_prompt_content(self, prompt_file: Path) -> str:
        """加载prompt文件内容"""
        try:
            return prompt_file.read_text(encoding='utf-8')
        except Exception as e:
            raise RuntimeError(f"无法读取prompt文件 {prompt_file}: {e}")
    
    def format_ai_request(self, prompt_content: str, input_text: str, step_num: int, total_steps: int) -> str:
        """
        格式化AI请求

        Args:
            prompt_content: prompt文件内容
            input_text: 输入文本
            step_num: 当前步骤号
            total_steps: 总步骤数

        Returns:
            格式化的AI请求字符串
        """
        separator = "=" * 60

        # 截断过长的prompt内容，只保留核心部分
        if len(prompt_content) > 2000:
            # 查找系统提示词部分
            lines = prompt_content.split('\n')
            core_lines = []
            in_system_prompt = False

            for line in lines:
                if '## 系统提示词' in line or '# 系统提示词' in line:
                    in_system_prompt = True
                    core_lines.append(line)
                elif in_system_prompt and line.startswith('#'):
                    # 遇到下一个标题，停止
                    break
                elif in_system_prompt:
                    core_lines.append(line)
                    # 限制行数，避免过长
                    if len(core_lines) > 30:
                        core_lines.append("...")
                        break

            if core_lines:
                prompt_content = '\n'.join(core_lines)
            else:
                # 如果没找到系统提示词，就截断前面部分
                prompt_content = prompt_content[:2000] + "\n\n[内容已截断...]"

        # 截断过长的输入文本
        if len(input_text) > 1000:
            input_text = input_text[:1000] + "\n\n[内容已截断...]"

        request = f"""
{separator}
🔄 PROMPT PIPELINE - 步骤 {step_num}/{total_steps}
{separator}

📋 PROMPT 内容：
{prompt_content}

📝 输入文本：
{input_text}

{separator}
请根据上述prompt处理输入文本，直接输出处理结果：
"""
        return request
    
    def save_output(self, content: str, prompt_chain: List[str]) -> Path:
        """
        保存输出内容到文件
        
        Args:
            content: 要保存的内容
            prompt_chain: prompt链，用于生成文件名
            
        Returns:
            保存的文件路径
        """
        # 创建今天的输出目录
        today = datetime.now().strftime("%Y-%m-%d")
        today_dir = self.output_dir / today
        today_dir.mkdir(parents=True, exist_ok=True)
        
        # 生成文件名
        timestamp = datetime.now().strftime("%H%M%S")
        last_prompt = prompt_chain[-1] if prompt_chain else "output"
        # 清理文件名中的特殊字符
        clean_prompt_name = re.sub(r'[^\w\-_\u4e00-\u9fff]', '_', last_prompt)
        filename = f"{timestamp}_{clean_prompt_name}.md"
        
        # 保存文件
        output_path = today_dir / filename
        output_path.write_text(content, encoding='utf-8')
        
        return output_path
    
    def execute_pipeline(self, command: str) -> str:
        """
        执行prompt管道
        
        Args:
            command: 管道命令
            
        Returns:
            执行结果信息
        """
        try:
            # 解析命令
            input_text, prompt_steps = self.parse_pipeline_command(command)
            
            # 检查是否有保存指令
            save_file = False
            actual_prompts = []
            
            for step in prompt_steps:
                if step.lower() in ['保存文件', '保存', 'save', 'save file']:
                    save_file = True
                else:
                    actual_prompts.append(step)
            
            if not actual_prompts:
                return "❌ 错误：没有找到有效的prompt步骤"
            
            # 显示管道概览
            pipeline_overview = f"""
🚀 启动 Prompt Pipeline
📊 输入文本：{input_text[:50]}{'...' if len(input_text) > 50 else ''}
🔗 处理链：{' → '.join(actual_prompts)}
💾 保存文件：{'是' if save_file else '否'}

开始处理...
"""
            print(pipeline_overview)
            
            # 逐步处理每个prompt
            current_content = input_text
            
            for i, prompt_name in enumerate(actual_prompts, 1):
                print(f"\n🔄 处理步骤 {i}/{len(actual_prompts)}: {prompt_name}")
                
                # 查找prompt文件
                prompt_file = self.find_prompt_file(prompt_name)
                if not prompt_file:
                    return f"❌ 错误：找不到prompt文件 '{prompt_name}'"
                
                print(f"📁 使用prompt文件：{prompt_file.relative_to(self.prompt_dir)}")
                
                # 加载prompt内容
                prompt_content = self.load_prompt_content(prompt_file)
                
                # 格式化AI请求
                ai_request = self.format_ai_request(
                    prompt_content, 
                    current_content, 
                    i, 
                    len(actual_prompts)
                )
                
                # 输出AI请求
                print(ai_request)
                
                # 等待用户输入AI响应
                print("\n⏳ 请将上述请求发送给AI，然后将AI的响应粘贴到下面：")
                print("（输入完成后按 Ctrl+D (Mac/Linux) 或 Ctrl+Z+Enter (Windows)）")
                
                try:
                    ai_response = sys.stdin.read().strip()
                    if not ai_response:
                        return f"❌ 错误：步骤 {i} 没有收到AI响应"
                    
                    current_content = ai_response
                    print(f"✅ 步骤 {i} 完成")
                    
                except KeyboardInterrupt:
                    return "❌ 用户中断了处理过程"
            
            # 处理保存文件
            if save_file:
                try:
                    output_path = self.save_output(current_content, actual_prompts)
                    result = f"""
🎉 Pipeline 执行完成！

💾 文件已保存到：{output_path}
📁 相对路径：{output_path.relative_to(self.prompt_dir)}

最终输出内容：
{'-' * 40}
{current_content}
{'-' * 40}
"""
                except Exception as e:
                    result = f"""
⚠️ Pipeline 执行完成，但保存文件时出错：{e}

最终输出内容：
{'-' * 40}
{current_content}
{'-' * 40}
"""
            else:
                result = f"""
🎉 Pipeline 执行完成！

最终输出内容：
{'-' * 40}
{current_content}
{'-' * 40}
"""
            
            return result
            
        except Exception as e:
            return f"❌ 执行出错：{e}"


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="Prompt Pipeline - 会话内Prompt工作流引擎",
        epilog="示例: python prompt_pipeline.py \"你的文字 |> 文章改写 |> 极简侘寂 |> 保存文件\""
    )
    parser.add_argument(
        "command",
        help="管道命令，格式：'输入文字 |> prompt1 |> prompt2 |> ...'",
        nargs='?'
    )
    parser.add_argument(
        "--list-prompts",
        action="store_true",
        help="列出所有可用的prompt"
    )
    
    args = parser.parse_args()
    
    pipeline = PromptPipeline()
    
    if args.list_prompts:
        print("📋 可用的Prompt列表：")
        print("-" * 40)
        for md_file in pipeline.prompt_dir.rglob("*.md"):
            if md_file.name == "README.md" or "output" in md_file.parts:
                continue
            relative_path = md_file.relative_to(pipeline.prompt_dir)
            prompt_name = str(relative_path).replace('.md', '')
            print(f"  • {prompt_name}")
            print(f"    文件：{relative_path}")
            print()
        return
    
    if not args.command:
        print("❌ 错误：请提供管道命令")
        print("使用 --help 查看帮助信息")
        print("使用 --list-prompts 查看可用的prompt")
        return
    
    result = pipeline.execute_pipeline(args.command)
    print(result)


if __name__ == "__main__":
    main()
