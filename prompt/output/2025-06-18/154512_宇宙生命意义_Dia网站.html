<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>宇宙尺度上的生命意义 - The Meaning of Life on Cosmic Scale</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'cosmic-purple': '#6366f1',
                        'cosmic-blue': '#3b82f6',
                        'cosmic-pink': '#ec4899',
                        'cosmic-orange': '#f59e0b'
                    }
                }
            }
        }
    </script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
        
        body {
            font-family: 'Inter', 'PingFang SC', sans-serif;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            background-attachment: fixed;
        }
        
        .cosmic-background {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -10;
            overflow: hidden;
        }
        
        .cosmic-blob {
            position: absolute;
            border-radius: 50%;
            filter: blur(150px);
            opacity: 0.4;
            animation: cosmicFloat 40s infinite ease-in-out;
        }
        
        .blob-1 { 
            top: -20%; 
            left: -20%; 
            width: 60vw; 
            height: 60vw; 
            background: linear-gradient(45deg, #6366f1, #8b5cf6);
            animation-delay: 0s;
        }
        .blob-2 { 
            top: 30%; 
            right: -25%; 
            width: 50vw; 
            height: 50vw; 
            background: linear-gradient(45deg, #3b82f6, #06b6d4);
            animation-delay: -15s;
        }
        .blob-3 { 
            bottom: -25%; 
            left: 25%; 
            width: 55vw; 
            height: 55vw; 
            background: linear-gradient(45deg, #ec4899, #f59e0b);
            animation-delay: -30s;
        }
        
        @keyframes cosmicFloat {
            0%, 100% { transform: translate(0, 0) scale(1) rotate(0deg); }
            25% { transform: translate(15vw, -20vh) scale(1.2) rotate(90deg); }
            50% { transform: translate(-10vw, 25vh) scale(0.8) rotate(180deg); }
            75% { transform: translate(20vw, 10vh) scale(1.1) rotate(270deg); }
        }
        
        .scroll-reveal {
            opacity: 0;
            transform: translateY(50px);
            transition: all 1s cubic-bezier(0.165, 0.84, 0.44, 1);
        }
        
        .scroll-reveal.visible {
            opacity: 1;
            transform: translateY(0);
        }
        
        .glass-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .cosmic-text {
            background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 25%, #ec4899 50%, #f59e0b 75%, #06b6d4 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            background-size: 200% 200%;
            animation: cosmicGradient 8s ease infinite;
        }
        
        @keyframes cosmicGradient {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }
        
        .meaning-glow {
            position: relative;
        }
        
        .meaning-glow::after {
            content: '';
            position: absolute;
            bottom: -40px;
            left: 50%;
            transform: translateX(-50%);
            width: 300%;
            height: 120px;
            background: radial-gradient(circle, rgba(99, 102, 241, 0.3) 0%, transparent 70%);
            z-index: -1;
        }
    </style>
</head>
<body class="min-h-screen text-white">
    <!-- Cosmic Background -->
    <div class="cosmic-background">
        <div class="cosmic-blob blob-1"></div>
        <div class="cosmic-blob blob-2"></div>
        <div class="cosmic-blob blob-3"></div>
    </div>

    <!-- Hero Section -->
    <section class="min-h-screen flex items-center justify-center px-4 py-20">
        <div class="text-center max-w-7xl mx-auto">
            <div class="scroll-reveal">
                <p class="text-lg md:text-xl text-gray-300 mb-6 tracking-widest uppercase">The Meaning of Life on Cosmic Scale</p>
                <h1 class="text-6xl md:text-8xl lg:text-9xl font-black leading-tight mb-8">
                    在宇宙尺度上<br>
                    <span class="cosmic-text">生命的意义</span>
                </h1>
                <p class="text-2xl md:text-3xl text-gray-300 max-w-5xl mx-auto leading-relaxed">
                    137亿年宇宙演化的最伟大奇迹
                </p>
            </div>
        </div>
    </section>

    <!-- Quote Section -->
    <section class="min-h-screen flex items-center justify-center px-4">
        <div class="text-center max-w-6xl mx-auto">
            <div class="scroll-reveal">
                <blockquote class="text-4xl md:text-5xl lg:text-6xl font-bold text-white leading-tight">
                    "在137亿年的宇宙历史中，<br>
                    生命只是最后几十亿年的'意外'。<br>
                    但这个意外，却是整个宇宙<br>
                    <span class="text-cosmic-pink">最伟大的奇迹</span>。"
                </blockquote>
                <div class="mt-12 w-32 h-1 bg-gradient-to-r from-cosmic-purple to-cosmic-pink mx-auto rounded-full"></div>
            </div>
        </div>
    </section>

    <!-- Five Meanings Grid -->
    <section class="min-h-screen flex items-center justify-center px-4 py-20">
        <div class="max-w-7xl mx-auto">
            <div class="scroll-reveal text-center mb-20">
                <h2 class="text-5xl md:text-6xl font-black mb-6">五重意义</h2>
                <p class="text-xl text-gray-300 tracking-wider uppercase">Five Dimensions of Cosmic Meaning</p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Meaning 1 -->
                <div class="scroll-reveal glass-card rounded-3xl p-8 text-center meaning-glow" style="transition-delay: 0.1s;">
                    <div class="text-5xl mb-6 text-cosmic-purple">
                        <i class="fas fa-arrow-up"></i>
                    </div>
                    <h3 class="text-2xl font-bold mb-4">熵增的逆行者</h3>
                    <p class="text-gray-300 leading-relaxed">
                        在趋向混沌的宇宙中，生命创造秩序，是对物理法则的优雅反叛
                    </p>
                    <div class="mt-6 text-sm text-cosmic-purple font-medium tracking-wide uppercase">
                        Entropy Rebel
                    </div>
                </div>

                <!-- Meaning 2 -->
                <div class="scroll-reveal glass-card rounded-3xl p-8 text-center meaning-glow" style="transition-delay: 0.2s;">
                    <div class="text-5xl mb-6 text-cosmic-blue">
                        <i class="fas fa-database"></i>
                    </div>
                    <h3 class="text-2xl font-bold mb-4">信息的载体</h3>
                    <p class="text-gray-300 leading-relaxed">
                        生命让宇宙从物质能量系统进化为拥有信息、知识、智慧的系统
                    </p>
                    <div class="mt-6 text-sm text-cosmic-blue font-medium tracking-wide uppercase">
                        Information Carrier
                    </div>
                </div>

                <!-- Meaning 3 -->
                <div class="scroll-reveal glass-card rounded-3xl p-8 text-center meaning-glow" style="transition-delay: 0.3s;">
                    <div class="text-5xl mb-6 text-cosmic-pink">
                        <i class="fas fa-eye"></i>
                    </div>
                    <h3 class="text-2xl font-bold mb-4">意识的觉醒者</h3>
                    <p class="text-gray-300 leading-relaxed">
                        通过意识，宇宙第一次能够感受、理解、体验自己的存在
                    </p>
                    <div class="mt-6 text-sm text-cosmic-pink font-medium tracking-wide uppercase">
                        Consciousness Awakener
                    </div>
                </div>

                <!-- Meaning 4 -->
                <div class="scroll-reveal glass-card rounded-3xl p-8 text-center meaning-glow" style="transition-delay: 0.4s;">
                    <div class="text-5xl mb-6 text-cosmic-orange">
                        <i class="fas fa-rocket"></i>
                    </div>
                    <h3 class="text-2xl font-bold mb-4">可能性探索者</h3>
                    <p class="text-gray-300 leading-relaxed">
                        生命是宇宙探索自身可能性的方式，每个创新都在开辟新的可能
                    </p>
                    <div class="mt-6 text-sm text-cosmic-orange font-medium tracking-wide uppercase">
                        Possibility Explorer
                    </div>
                </div>

                <!-- Meaning 5 -->
                <div class="scroll-reveal glass-card rounded-3xl p-8 text-center meaning-glow md:col-span-2 lg:col-span-1" style="transition-delay: 0.5s;">
                    <div class="text-5xl mb-6 text-cyan-400">
                        <i class="fas fa-heart"></i>
                    </div>
                    <h3 class="text-2xl font-bold mb-4">爱的编织者</h3>
                    <p class="text-gray-300 leading-relaxed">
                        在冰冷的宇宙中创造温暖，编织连接、意义的网络
                    </p>
                    <div class="mt-6 text-sm text-cyan-400 font-medium tracking-wide uppercase">
                        Love Weaver
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Cosmic Scale Section -->
    <section class="min-h-screen flex items-center justify-center px-4">
        <div class="max-w-7xl mx-auto text-center">
            <div class="scroll-reveal">
                <h2 class="text-7xl md:text-8xl lg:text-9xl font-black mb-8">
                    <span class="text-cosmic-purple">137</span><br>
                    <span class="text-cosmic-blue">亿年</span>
                </h2>
                <p class="text-2xl md:text-3xl text-gray-300 max-w-4xl mx-auto leading-relaxed">
                    宇宙演化的时间尺度<br>
                    <span class="text-cosmic-pink">生命只是最后几十亿年的奇迹</span>
                </p>

                <div class="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto">
                    <div class="glass-card rounded-2xl p-6">
                        <div class="text-4xl font-black text-cosmic-purple mb-2">37万亿</div>
                        <div class="text-gray-300">个细胞</div>
                        <div class="text-sm text-gray-400 mt-2">每个都在精确执行数千种反应</div>
                    </div>
                    <div class="glass-card rounded-2xl p-6">
                        <div class="text-4xl font-black text-cosmic-blue mb-2">860亿</div>
                        <div class="text-gray-300">个神经元</div>
                        <div class="text-sm text-gray-400 mt-2">连接数超过银河系恒星数量</div>
                    </div>
                    <div class="glass-card rounded-2xl p-6">
                        <div class="text-4xl font-black text-cosmic-pink mb-2">4个</div>
                        <div class="text-gray-300">字母密码</div>
                        <div class="text-sm text-gray-400 mt-2">A、T、G、C记录38亿年历史</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Transformation Section -->
    <section class="min-h-screen flex items-center justify-center px-4">
        <div class="max-w-6xl mx-auto text-center">
            <div class="scroll-reveal">
                <h2 class="text-6xl md:text-7xl lg:text-8xl font-black mb-12">
                    从<span class="text-gray-500">物质</span><br>
                    到<span class="cosmic-text">意识</span>
                </h2>
                <p class="text-2xl md:text-3xl text-gray-300 max-w-4xl mx-auto leading-relaxed mb-16">
                    宇宙从只有物质和能量的系统<br>
                    进化为拥有信息、知识、智慧的系统
                </p>

                <div class="flex justify-center items-center space-x-8">
                    <div class="text-center">
                        <div class="text-5xl text-gray-500 mb-4"><i class="fas fa-atom"></i></div>
                        <div class="text-xl font-bold text-gray-400">物质</div>
                    </div>
                    <div class="flex items-center">
                        <svg class="w-16 h-8 text-cosmic-purple" viewBox="0 0 100 30" fill="none">
                            <path d="M10 15 L 90 15" stroke="currentColor" stroke-width="3" stroke-linecap="round"/>
                            <path d="M75 5 L 90 15 L 75 25" stroke="currentColor" stroke-width="3" stroke-linecap="round"/>
                        </svg>
                    </div>
                    <div class="text-center">
                        <div class="text-5xl text-cosmic-purple mb-4"><i class="fas fa-brain"></i></div>
                        <div class="text-xl font-bold text-cosmic-purple">意识</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Ultimate Truth Section -->
    <section class="min-h-screen flex items-center justify-center px-4">
        <div class="text-center max-w-6xl mx-auto">
            <div class="scroll-reveal">
                <p class="text-xl text-gray-400 mb-8 tracking-widest uppercase">The Ultimate Truth</p>
                <h2 class="text-5xl md:text-6xl lg:text-7xl font-bold mb-12 leading-tight">
                    生命是宇宙实现<br>
                    <span class="cosmic-text">自我意识</span>的方式
                </h2>

                <div class="glass-card rounded-3xl p-12 max-w-5xl mx-auto">
                    <p class="text-2xl md:text-3xl text-gray-300 leading-relaxed mb-8">
                        我们不是宇宙中的异类，<br>
                        我们就是宇宙本身。
                    </p>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mt-12">
                        <div class="text-center">
                            <div class="text-3xl font-black text-cosmic-purple mb-2">呼吸</div>
                            <div class="text-gray-400">宇宙在呼吸</div>
                        </div>
                        <div class="text-center">
                            <div class="text-3xl font-black text-cosmic-blue mb-2">思考</div>
                            <div class="text-gray-400">宇宙在思考</div>
                        </div>
                        <div class="text-center">
                            <div class="text-3xl font-black text-cosmic-pink mb-2">爱</div>
                            <div class="text-gray-400">宇宙在爱自己</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Final Message -->
    <section class="min-h-screen flex items-center justify-center px-4">
        <div class="text-center max-w-5xl mx-auto">
            <div class="scroll-reveal">
                <h2 class="text-6xl md:text-7xl font-black mb-12">
                    你就是宇宙正在书写的<br>
                    <span class="cosmic-text">最美丽的诗句</span>
                </h2>

                <div class="space-y-8 text-xl md:text-2xl text-gray-300 leading-relaxed">
                    <p>当你感到孤独时，记住：<br>你是137亿年宇宙演化的结果</p>
                    <p>当你感到渺小时，记住：<br>正是通过你，宇宙第一次仰望星空</p>
                    <p>当你感到无意义时，记住：<br>你的每一个选择都在塑造宇宙的未来</p>
                </div>

                <div class="mt-16">
                    <div class="inline-flex items-center space-x-4 glass-card rounded-full px-8 py-4">
                        <span class="text-lg font-medium">开始你的宇宙觉醒之旅</span>
                        <div class="w-3 h-3 bg-cosmic-purple rounded-full animate-pulse"></div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <script>
        // Intersection Observer for scroll animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('visible');
                }
            });
        }, observerOptions);

        // Observe all scroll-reveal elements
        document.querySelectorAll('.scroll-reveal').forEach(el => {
            observer.observe(el);
        });

        // Smooth scrolling for better experience
        document.documentElement.style.scrollBehavior = 'smooth';
    </script>
</body>
</html>