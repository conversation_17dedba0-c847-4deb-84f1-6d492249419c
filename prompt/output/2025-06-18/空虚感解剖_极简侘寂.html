<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>当代人的隐形瘟疫：空虚感的三重解剖</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'wabi-gray': '#F5F3F0',
                        'wabi-beige': '#E8E2D5',
                        'wabi-earth': '#D4C4A8',
                        'wabi-concrete': '#C7BDB1',
                        'wabi-charcoal': '#4A4A4A',
                        'wabi-carbon': '#2D2D2D',
                        'wabi-accent': '#8B7355'
                    },
                    fontFamily: {
                        'chinese': ['PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'sans-serif'],
                        'english': ['SF Pro Display', 'Helvetica Neue', 'Arial', 'sans-serif']
                    }
                }
            }
        }
    </script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');

        body {
            font-family: 'Inter', 'PingFang SC', sans-serif;
            background: linear-gradient(135deg, #F5F3F0 0%, #E8E2D5 100%);
            background-attachment: fixed;
        }

        .hero-text {
            background: linear-gradient(135deg, #2D2D2D 0%, #4A4A4A 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .accent-glow {
            box-shadow: 0 0 40px rgba(139, 115, 85, 0.3);
        }

        .floating {
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .fade-in {
            animation: fadeIn 1s ease-out forwards;
            opacity: 0;
        }

        @keyframes fadeIn {
            to { opacity: 1; }
        }

        .slide-up {
            animation: slideUp 0.8s ease-out forwards;
            transform: translateY(30px);
            opacity: 0;
        }

        @keyframes slideUp {
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        .texture-overlay {
            background-image:
                radial-gradient(circle at 25% 25%, rgba(139, 115, 85, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(212, 196, 168, 0.1) 0%, transparent 50%);
        }
    </style>
</head>
<body class="min-h-screen bg-wabi-gray texture-overlay">
    <!-- Hero Section -->
    <section class="min-h-screen flex items-center justify-center px-4 py-20">
        <div class="max-w-7xl mx-auto grid grid-cols-1 lg:grid-cols-12 gap-8 items-center">
            <!-- Main Title -->
            <div class="lg:col-span-8 space-y-8">
                <div class="fade-in" style="animation-delay: 0.2s;">
                    <h1 class="text-6xl lg:text-8xl font-black hero-text leading-tight">
                        隐形<br>
                        <span class="text-wabi-accent">瘟疫</span>
                    </h1>
                    <p class="text-xl lg:text-2xl text-wabi-charcoal mt-4 font-light">
                        The Invisible Epidemic
                    </p>
                </div>

                <div class="slide-up" style="animation-delay: 0.6s;">
                    <blockquote class="text-2xl lg:text-3xl text-wabi-carbon font-medium italic border-l-4 border-wabi-accent pl-6">
                        "我们生活在一个前所未有的丰富时代，<br>
                        却感受着前所未有的贫瘠。"
                    </blockquote>
                </div>
            </div>

            <!-- Stats Visualization -->
            <div class="lg:col-span-4 space-y-6">
                <div class="floating bg-white/60 backdrop-blur-sm rounded-3xl p-8 accent-glow">
                    <div class="text-center">
                        <div class="text-6xl font-black text-wabi-accent mb-2">3</div>
                        <div class="text-lg text-wabi-charcoal font-medium">重解剖维度</div>
                        <div class="text-sm text-wabi-carbon/70 font-english">Dimensions</div>
                    </div>
                </div>

                <div class="floating bg-white/40 backdrop-blur-sm rounded-3xl p-6" style="animation-delay: 0.5s;">
                    <div class="flex items-center space-x-4">
                        <i class="fas fa-brain text-3xl text-wabi-accent"></i>
                        <div>
                            <div class="text-2xl font-bold text-wabi-carbon">意义稀释</div>
                            <div class="text-sm text-wabi-charcoal font-english">Meaning Dilution</div>
                        </div>
                    </div>
                </div>

                <div class="floating bg-white/40 backdrop-blur-sm rounded-3xl p-6" style="animation-delay: 1s;">
                    <div class="flex items-center space-x-4">
                        <i class="fas fa-unlink text-3xl text-wabi-accent"></i>
                        <div>
                            <div class="text-2xl font-bold text-wabi-carbon">连接断裂</div>
                            <div class="text-sm text-wabi-charcoal font-english">Connection Fracture</div>
                        </div>
                    </div>
                </div>

                <div class="floating bg-white/40 backdrop-blur-sm rounded-3xl p-6" style="animation-delay: 1.5s;">
                    <div class="flex items-center space-x-4">
                        <i class="fas fa-running text-3xl text-wabi-accent"></i>
                        <div>
                            <div class="text-2xl font-bold text-wabi-carbon">当下逃离</div>
                            <div class="text-sm text-wabi-charcoal font-english">Present Escape</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Analysis Sections -->
    <section class="py-20 px-4">
        <div class="max-w-7xl mx-auto">
            <!-- Section 1: 意义的稀释 -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-12 mb-32">
                <div class="lg:col-span-2 space-y-8">
                    <div class="slide-up">
                        <h2 class="text-5xl lg:text-6xl font-black text-wabi-carbon mb-4">
                            第一重解剖
                        </h2>
                        <h3 class="text-3xl lg:text-4xl font-bold text-wabi-accent mb-6">
                            意义的稀释
                        </h3>
                        <p class="text-xl text-wabi-charcoal leading-relaxed">
                            现代社会最大的残酷，不是剥夺了我们追求意义的权利，而是给了我们太多选择，却没有给我们选择的智慧。
                        </p>
                    </div>

                    <div class="slide-up bg-white/50 backdrop-blur-sm rounded-2xl p-8" style="animation-delay: 0.3s;">
                        <p class="text-lg text-wabi-carbon leading-relaxed">
                            想象一下我们的祖辈：一个农民知道自己要种地养家，一个工匠知道自己要传承手艺，一个母亲知道自己要抚育后代。他们的人生轨迹虽然受限，但<strong>目标明确，意义清晰</strong>。
                        </p>
                    </div>
                </div>

                <div class="space-y-6">
                    <div class="floating bg-gradient-to-br from-wabi-accent/20 to-wabi-earth/20 rounded-3xl p-8 text-center">
                        <div class="text-5xl font-black text-wabi-accent mb-2">∞</div>
                        <div class="text-lg font-medium text-wabi-carbon">无限选择</div>
                        <div class="text-sm text-wabi-charcoal font-english">Infinite Choices</div>
                    </div>

                    <div class="floating bg-gradient-to-br from-wabi-carbon/10 to-wabi-charcoal/10 rounded-3xl p-8 text-center" style="animation-delay: 0.5s;">
                        <div class="text-5xl font-black text-wabi-carbon mb-2">0</div>
                        <div class="text-lg font-medium text-wabi-carbon">选择智慧</div>
                        <div class="text-sm text-wabi-charcoal font-english">Choice Wisdom</div>
                    </div>
                </div>
            </div>

            <!-- Section 2: 连接的断裂 -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-12 mb-32">
                <div class="space-y-6">
                    <div class="floating bg-gradient-to-br from-blue-100 to-blue-200 rounded-3xl p-8 text-center">
                        <div class="text-5xl font-black text-blue-600 mb-2">500</div>
                        <div class="text-lg font-medium text-wabi-carbon">"朋友"</div>
                        <div class="text-sm text-wabi-charcoal font-english">Friends</div>
                    </div>

                    <div class="floating bg-gradient-to-br from-red-100 to-red-200 rounded-3xl p-8 text-center" style="animation-delay: 0.5s;">
                        <div class="text-5xl font-black text-red-600 mb-2">0</div>
                        <div class="text-lg font-medium text-wabi-carbon">真正连接</div>
                        <div class="text-sm text-wabi-charcoal font-english">Real Connection</div>
                    </div>
                </div>

                <div class="lg:col-span-2 space-y-8">
                    <div class="slide-up">
                        <h3 class="text-3xl lg:text-4xl font-bold text-wabi-accent mb-6">
                            连接的断裂
                        </h3>
                        <p class="text-xl text-wabi-charcoal leading-relaxed">
                            我们从未如此"连接"，却从未如此孤独。
                        </p>
                    </div>

                    <div class="slide-up bg-white/50 backdrop-blur-sm rounded-2xl p-8" style="animation-delay: 0.3s;">
                        <p class="text-lg text-wabi-carbon leading-relaxed">
                            我们生活在一个<strong>"浅连接"的时代</strong>。我们知道别人吃了什么早餐，去了哪里旅行，买了什么新衣服，但我们不知道他们内心的恐惧、真实的渴望、深层的痛苦。
                        </p>
                    </div>
                </div>
            </div>

            <!-- Section 3: 当下的逃离 -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-32">
                <div class="space-y-8">
                    <div class="slide-up">
                        <h3 class="text-3xl lg:text-4xl font-bold text-wabi-accent mb-6">
                            当下的逃离
                        </h3>
                        <p class="text-xl text-wabi-charcoal leading-relaxed">
                            我们活在过去的遗憾和未来的焦虑中，唯独不活在当下。
                        </p>
                    </div>

                    <div class="slide-up bg-white/50 backdrop-blur-sm rounded-2xl p-8" style="animation-delay: 0.3s;">
                        <p class="text-lg text-wabi-carbon leading-relaxed">
                            当下是唯一真实存在的时刻，但我们却把它当作需要被填满、被消费、被逃离的空白。
                        </p>
                    </div>
                </div>

                <div class="grid grid-cols-2 gap-4">
                    <div class="floating bg-gradient-to-br from-purple-100 to-purple-200 rounded-2xl p-6 text-center">
                        <i class="fas fa-mobile-alt text-3xl text-purple-600 mb-3"></i>
                        <div class="text-lg font-medium text-wabi-carbon">刷手机</div>
                        <div class="text-sm text-wabi-charcoal">逃离无聊</div>
                    </div>

                    <div class="floating bg-gradient-to-br from-green-100 to-green-200 rounded-2xl p-6 text-center" style="animation-delay: 0.2s;">
                        <i class="fas fa-tv text-3xl text-green-600 mb-3"></i>
                        <div class="text-lg font-medium text-wabi-carbon">追剧</div>
                        <div class="text-sm text-wabi-charcoal">逃离现实</div>
                    </div>

                    <div class="floating bg-gradient-to-br from-yellow-100 to-yellow-200 rounded-2xl p-6 text-center" style="animation-delay: 0.4s;">
                        <i class="fas fa-shopping-cart text-3xl text-yellow-600 mb-3"></i>
                        <div class="text-lg font-medium text-wabi-carbon">购物</div>
                        <div class="text-sm text-wabi-charcoal">逃离情绪</div>
                    </div>

                    <div class="floating bg-gradient-to-br from-indigo-100 to-indigo-200 rounded-2xl p-6 text-center" style="animation-delay: 0.6s;">
                        <i class="fas fa-briefcase text-3xl text-indigo-600 mb-3"></i>
                        <div class="text-lg font-medium text-wabi-carbon">工作</div>
                        <div class="text-sm text-wabi-charcoal">逃离思考</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Solution Section -->
    <section class="py-20 px-4 bg-gradient-to-br from-wabi-concrete/30 to-wabi-earth/30">
        <div class="max-w-7xl mx-auto text-center">
            <div class="slide-up mb-16">
                <h2 class="text-5xl lg:text-6xl font-black text-wabi-carbon mb-8">
                    破局之道
                </h2>
                <p class="text-2xl text-wabi-charcoal">
                    三个反直觉的真相
                </p>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <div class="slide-up bg-white/60 backdrop-blur-sm rounded-3xl p-8 accent-glow" style="animation-delay: 0.2s;">
                    <div class="text-4xl font-black text-wabi-accent mb-4">01</div>
                    <h3 class="text-2xl font-bold text-wabi-carbon mb-4">拥抱约束</h3>
                    <p class="text-lg text-wabi-charcoal leading-relaxed">
                        给自己设定边界，在有限中寻找无限。选择一个值得为之奋斗的目标。
                    </p>
                </div>

                <div class="slide-up bg-white/60 backdrop-blur-sm rounded-3xl p-8 accent-glow" style="animation-delay: 0.4s;">
                    <div class="text-4xl font-black text-wabi-accent mb-4">02</div>
                    <h3 class="text-2xl font-bold text-wabi-carbon mb-4">深化连接</h3>
                    <p class="text-lg text-wabi-charcoal leading-relaxed">
                        减少浅层社交，增加深度对话。专注自己的内心体验。
                    </p>
                </div>

                <div class="slide-up bg-white/60 backdrop-blur-sm rounded-3xl p-8 accent-glow" style="animation-delay: 0.6s;">
                    <div class="text-4xl font-black text-wabi-accent mb-4">03</div>
                    <h3 class="text-2xl font-bold text-wabi-carbon mb-4">回归当下</h3>
                    <p class="text-lg text-wabi-charcoal leading-relaxed">
                        学会与无聊共处，在平凡的日常中发现不平凡的美好。
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Final CTA -->
    <section class="py-20 px-4">
        <div class="max-w-4xl mx-auto text-center">
            <div class="slide-up">
                <h2 class="text-4xl lg:text-5xl font-black text-wabi-carbon mb-8">
                    空虚感不是现代生活的副产品，<br>
                    <span class="text-wabi-accent">而是我们选择的结果。</span>
                </h2>

                <div class="bg-white/60 backdrop-blur-sm rounded-3xl p-12 accent-glow mt-12">
                    <p class="text-2xl text-wabi-carbon leading-relaxed mb-8">
                        当我们选择深度而非广度，选择真实而非完美，选择当下而非逃离时，空虚感就会像雾气一样散去，露出生活本来的面目：
                    </p>
                    <p class="text-3xl font-bold text-wabi-accent">
                        充实、有意义、值得被好好体验。
                    </p>
                </div>

                <div class="mt-16">
                    <h3 class="text-3xl font-bold text-wabi-carbon mb-4">
                        所以，今天你选择什么？
                    </h3>
                    <p class="text-lg text-wabi-charcoal font-english">
                        What will you choose today?
                    </p>
                </div>
            </div>
        </div>
    </section>

    <script>
        // 添加滚动触发动画
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.animationPlayState = 'running';
                }
            });
        }, observerOptions);

        // 观察所有需要动画的元素
        document.querySelectorAll('.slide-up, .fade-in').forEach(el => {
            observer.observe(el);
        });
    </script>
</body>
</html>