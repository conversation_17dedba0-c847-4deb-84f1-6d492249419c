<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>当代人的隐形瘟疫：空虚感的三重解剖</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'wabi-gray': '#F5F3F0',
                        'wabi-beige': '#E8E2D5',
                        'wabi-earth': '#D4C4A8',
                        'wabi-concrete': '#C7BDB1',
                        'wabi-charcoal': '#4A4A4A',
                        'wabi-carbon': '#2D2D2D',
                        'wabi-accent': '#8B7355'
                    },
                    fontFamily: {
                        'chinese': ['PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'sans-serif'],
                        'english': ['SF Pro Display', 'Helvetica Neue', 'Arial', 'sans-serif']
                    }
                }
            }
        }
    </script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');

        body {
            font-family: 'Inter', 'PingFang SC', sans-serif;
            background: linear-gradient(135deg, #F5F3F0 0%, #E8E2D5 100%);
            background-attachment: fixed;
        }

        .hero-text {
            background: linear-gradient(135deg, #2D2D2D 0%, #4A4A4A 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .accent-glow {
            box-shadow: 0 0 40px rgba(139, 115, 85, 0.3);
        }

        .floating {
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .fade-in {
            animation: fadeIn 1s ease-out forwards;
            opacity: 0;
        }

        @keyframes fadeIn {
            to { opacity: 1; }
        }

        .slide-up {
            animation: slideUp 0.8s ease-out forwards;
            transform: translateY(30px);
            opacity: 0;
        }

        @keyframes slideUp {
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        .texture-overlay {
            background-image:
                radial-gradient(circle at 25% 25%, rgba(139, 115, 85, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(212, 196, 168, 0.1) 0%, transparent 50%);
        }
    </style>
</head>
<body class="min-h-screen bg-wabi-gray texture-overlay">
    <!-- Hero Section -->
    <section class="min-h-screen flex items-center justify-center px-4 py-20">
        <div class="max-w-7xl mx-auto grid grid-cols-1 lg:grid-cols-12 gap-8 items-center">
            <!-- Main Title -->
            <div class="lg:col-span-8 space-y-8">
                <div class="fade-in" style="animation-delay: 0.2s;">
                    <h1 class="text-6xl lg:text-8xl font-black hero-text leading-tight">
                        隐形<br>
                        <span class="text-wabi-accent">瘟疫</span>
                    </h1>
                    <p class="text-xl lg:text-2xl text-wabi-charcoal mt-4 font-light">
                        The Invisible Epidemic
                    </p>
                </div>

                <div class="slide-up" style="animation-delay: 0.6s;">
                    <blockquote class="text-2xl lg:text-3xl text-wabi-carbon font-medium italic border-l-4 border-wabi-accent pl-6">
                        "我们生活在一个前所未有的丰富时代，<br>
                        却感受着前所未有的贫瘠。"
                    </blockquote>
                </div>
            </div>

            <!-- Stats Visualization -->
            <div class="lg:col-span-4 space-y-6">
                <div class="floating bg-white/60 backdrop-blur-sm rounded-3xl p-8 accent-glow">
                    <div class="text-center">
                        <div class="text-6xl font-black text-wabi-accent mb-2">3</div>
                        <div class="text-lg text-wabi-charcoal font-medium">重解剖维度</div>
                        <div class="text-sm text-wabi-carbon/70 font-english">Dimensions</div>
                    </div>
                </div>

                <div class="floating bg-white/40 backdrop-blur-sm rounded-3xl p-6" style="animation-delay: 0.5s;">
                    <div class="flex items-center space-x-4">
                        <i class="fas fa-brain text-3xl text-wabi-accent"></i>
                        <div>
                            <div class="text-2xl font-bold text-wabi-carbon">意义稀释</div>
                            <div class="text-sm text-wabi-charcoal font-english">Meaning Dilution</div>
                        </div>
                    </div>
                </div>

                <div class="floating bg-white/40 backdrop-blur-sm rounded-3xl p-6" style="animation-delay: 1s;">
                    <div class="flex items-center space-x-4">
                        <i class="fas fa-unlink text-3xl text-wabi-accent"></i>
                        <div>
                            <div class="text-2xl font-bold text-wabi-carbon">连接断裂</div>
                            <div class="text-sm text-wabi-charcoal font-english">Connection Fracture</div>
                        </div>
                    </div>
                </div>

                <div class="floating bg-white/40 backdrop-blur-sm rounded-3xl p-6" style="animation-delay: 1.5s;">
                    <div class="flex items-center space-x-4">
                        <i class="fas fa-running text-3xl text-wabi-accent"></i>
                        <div>
                            <div class="text-2xl font-bold text-wabi-carbon">当下逃离</div>
                            <div class="text-sm text-wabi-charcoal font-english">Present Escape</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>