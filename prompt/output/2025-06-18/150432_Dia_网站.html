<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>当焦虑成为过去式 - When Anxiety Becomes Past Tense</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'aurora-pink': '#ff7e5f',
                        'aurora-blue': '#6a82fb',
                        'aurora-purple': '#d83bff',
                        'aurora-orange': '#feca57'
                    }
                }
            }
        }
    </script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
        
        body {
            font-family: 'Inter', 'PingFang SC', sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            background-attachment: fixed;
        }
        
        .aurora-background {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -10;
            overflow: hidden;
        }
        
        .aurora-blob {
            position: absolute;
            border-radius: 50%;
            filter: blur(120px);
            opacity: 0.3;
            animation: float 25s infinite ease-in-out;
        }
        
        .blob-1 { 
            top: -10%; 
            left: -10%; 
            width: 40vw; 
            height: 40vw; 
            background: linear-gradient(45deg, #ff7e5f, #feb47b);
            animation-delay: 0s;
        }
        .blob-2 { 
            top: 20%; 
            right: -15%; 
            width: 35vw; 
            height: 35vw; 
            background: linear-gradient(45deg, #6a82fb, #fc5c7d);
            animation-delay: -8s;
        }
        .blob-3 { 
            bottom: -15%; 
            left: 20%; 
            width: 45vw; 
            height: 45vw; 
            background: linear-gradient(45deg, #d83bff, #667eea);
            animation-delay: -16s;
        }
        
        @keyframes float {
            0%, 100% { transform: translate(0, 0) scale(1); }
            25% { transform: translate(10vw, -10vh) scale(1.1); }
            50% { transform: translate(-5vw, 15vh) scale(0.9); }
            75% { transform: translate(15vw, 5vh) scale(1.05); }
        }
        
        .scroll-reveal {
            opacity: 0;
            transform: translateY(40px);
            transition: all 0.8s cubic-bezier(0.165, 0.84, 0.44, 1);
        }
        
        .scroll-reveal.visible {
            opacity: 1;
            transform: translateY(0);
        }
        
        .glass-card {
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .gradient-text {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .insight-glow {
            position: relative;
        }
        
        .insight-glow::after {
            content: '';
            position: absolute;
            bottom: -30px;
            left: 50%;
            transform: translateX(-50%);
            width: 200%;
            height: 80px;
            background: radial-gradient(circle, rgba(102, 126, 234, 0.2) 0%, transparent 70%);
            z-index: -1;
        }
    </style>
</head>
<body class="min-h-screen text-gray-900">
    <!-- Aurora Background -->
    <div class="aurora-background">
        <div class="aurora-blob blob-1"></div>
        <div class="aurora-blob blob-2"></div>
        <div class="aurora-blob blob-3"></div>
    </div>

    <!-- Hero Section -->
    <section class="min-h-screen flex items-center justify-center px-4 py-20">
        <div class="text-center max-w-6xl mx-auto">
            <div class="scroll-reveal">
                <p class="text-lg md:text-xl text-gray-500 mb-6 tracking-widest uppercase">When Anxiety Becomes Past Tense</p>
                <h1 class="text-6xl md:text-8xl lg:text-9xl font-black leading-tight mb-8">
                    当焦虑<br>
                    <span class="gradient-text">成为过去式</span>
                </h1>
                <p class="text-2xl md:text-3xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
                    那些让我们彻底解脱的顿悟时刻
                </p>
            </div>
        </div>
    </section>

    <!-- Quote Section -->
    <section class="min-h-screen flex items-center justify-center px-4">
        <div class="text-center max-w-5xl mx-auto">
            <div class="scroll-reveal">
                <blockquote class="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-800 leading-tight">
                    "焦虑，是我们对未知的恐惧<br>
                    在现在的<span class="text-purple-600">投射</span>。"
                </blockquote>
                <div class="mt-12 w-24 h-1 bg-gradient-to-r from-purple-500 to-blue-500 mx-auto rounded-full"></div>
            </div>
        </div>
    </section>

    <!-- Five Insights Grid -->
    <section class="min-h-screen flex items-center justify-center px-4 py-20">
        <div class="max-w-7xl mx-auto">
            <div class="scroll-reveal text-center mb-16">
                <h2 class="text-5xl md:text-6xl font-black mb-6">五个顿悟</h2>
                <p class="text-xl text-gray-600 tracking-wider uppercase">Five Life-Changing Insights</p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Insight 1 -->
                <div class="scroll-reveal glass-card rounded-3xl p-8 text-center insight-glow" style="transition-delay: 0.1s;">
                    <div class="text-5xl mb-6 text-blue-500">
                        <i class="fas fa-hand-paper"></i>
                    </div>
                    <h3 class="text-2xl font-bold mb-4">控制的边界</h3>
                    <p class="text-gray-600 leading-relaxed">
                        你唯一能控制的，就是你对不能控制之事的反应
                    </p>
                    <div class="mt-6 text-sm text-blue-600 font-medium tracking-wide uppercase">
                        Control Boundaries
                    </div>
                </div>

                <!-- Insight 2 -->
                <div class="scroll-reveal glass-card rounded-3xl p-8 text-center insight-glow" style="transition-delay: 0.2s;">
                    <div class="text-5xl mb-6 text-purple-500">
                        <i class="fas fa-gem"></i>
                    </div>
                    <h3 class="text-2xl font-bold mb-4">完美的幻象</h3>
                    <p class="text-gray-600 leading-relaxed">
                        完美是焦虑的温床，而"足够好"是内心平静的港湾
                    </p>
                    <div class="mt-6 text-sm text-purple-600 font-medium tracking-wide uppercase">
                        Perfect Illusion
                    </div>
                </div>

                <!-- Insight 3 -->
                <div class="scroll-reveal glass-card rounded-3xl p-8 text-center insight-glow" style="transition-delay: 0.3s;">
                    <div class="text-5xl mb-6 text-orange-500">
                        <i class="fas fa-clock"></i>
                    </div>
                    <h3 class="text-2xl font-bold mb-4">当下的力量</h3>
                    <p class="text-gray-600 leading-relaxed">
                        焦虑存在于过去的遗憾和未来的恐惧中，但生活只发生在当下
                    </p>
                    <div class="mt-6 text-sm text-orange-600 font-medium tracking-wide uppercase">
                        Present Power
                    </div>
                </div>

                <!-- Insight 4 -->
                <div class="scroll-reveal glass-card rounded-3xl p-8 text-center insight-glow" style="transition-delay: 0.4s;">
                    <div class="text-5xl mb-6 text-green-500">
                        <i class="fas fa-lightbulb"></i>
                    </div>
                    <h3 class="text-2xl font-bold mb-4">意义的重构</h3>
                    <p class="text-gray-600 leading-relaxed">
                        痛苦是不可避免的，但痛苦的意义是可以选择的
                    </p>
                    <div class="mt-6 text-sm text-green-600 font-medium tracking-wide uppercase">
                        Meaning Reconstruction
                    </div>
                </div>

                <!-- Insight 5 -->
                <div class="scroll-reveal glass-card rounded-3xl p-8 text-center insight-glow md:col-span-2 lg:col-span-1" style="transition-delay: 0.5s;">
                    <div class="text-5xl mb-6 text-pink-500">
                        <i class="fas fa-heart"></i>
                    </div>
                    <h3 class="text-2xl font-bold mb-4">连接的治愈</h3>
                    <p class="text-gray-600 leading-relaxed">
                        焦虑让我们孤立，而连接让我们治愈
                    </p>
                    <div class="mt-6 text-sm text-pink-600 font-medium tracking-wide uppercase">
                        Healing Connection
                    </div>
                </div>
            </div>
        </div>
    </section>