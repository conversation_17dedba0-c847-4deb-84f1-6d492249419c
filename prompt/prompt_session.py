#!/usr/bin/env python3
"""
Prompt Session - 会话内直接处理版本

这个版本专门为AI会话环境设计，直接输出格式化的prompt请求，
无需复杂的交互输入，更适合在当前会话中使用。
"""

import sys
import os
import re
from pathlib import Path
from datetime import datetime
from typing import List, Tuple, Optional
import argparse


class PromptSession:
    def __init__(self, prompt_dir: Path = None):
        """初始化Prompt会话处理器"""
        if prompt_dir is None:
            self.prompt_dir = Path(__file__).parent.resolve()
        else:
            self.prompt_dir = Path(prompt_dir).resolve()
        
        self.output_dir = self.prompt_dir / 'output'
        
    def parse_pipeline_command(self, command: str) -> Tuple[str, List[str]]:
        """解析管道命令"""
        parts = [part.strip() for part in command.split('|>')]
        
        if len(parts) < 2:
            raise ValueError("命令格式错误。正确格式：'输入文字 |> prompt1 |> prompt2 |> ...'")
        
        input_text = parts[0]
        prompt_steps = parts[1:]
        
        return input_text, prompt_steps
    
    def find_prompt_file(self, prompt_name: str) -> Optional[Path]:
        """智能查找prompt文件"""
        # 如果包含路径分隔符，直接使用相对路径
        if '/' in prompt_name or '\\' in prompt_name:
            prompt_path = self.prompt_dir / f"{prompt_name}.md"
            if prompt_path.exists():
                return prompt_path
        
        # 在所有子目录中搜索匹配的文件
        for md_file in self.prompt_dir.rglob("*.md"):
            # 跳过README.md和output目录
            if md_file.name == "README.md" or "output" in md_file.parts:
                continue
                
            # 检查文件名是否匹配（不包含扩展名）
            if md_file.stem == prompt_name:
                return md_file
                
            # 检查完整相对路径是否匹配
            relative_path = md_file.relative_to(self.prompt_dir)
            relative_path_str = str(relative_path).replace('.md', '')
            if relative_path_str == prompt_name:
                return md_file
        
        return None
    
    def load_prompt_content(self, prompt_file: Path) -> str:
        """加载prompt文件内容"""
        try:
            content = prompt_file.read_text(encoding='utf-8')
            
            # 提取核心系统提示词，去除冗长的示例
            lines = content.split('\n')
            core_lines = []
            in_system_prompt = False
            
            for line in lines:
                if '## 系统提示词' in line or '# 系统提示词' in line:
                    in_system_prompt = True
                    core_lines.append(line)
                elif in_system_prompt and line.startswith('#'):
                    # 遇到下一个标题，停止
                    break
                elif in_system_prompt:
                    core_lines.append(line)
                    # 限制行数，避免过长
                    if len(core_lines) > 30:
                        break
            
            if core_lines:
                return '\n'.join(core_lines)
            else:
                # 如果没找到系统提示词，返回前面部分
                return '\n'.join(lines[:20])
                
        except Exception as e:
            raise RuntimeError(f"无法读取prompt文件 {prompt_file}: {e}")
    
    def generate_session_request(self, command: str) -> str:
        """
        生成会话内处理请求
        
        Args:
            command: 管道命令
            
        Returns:
            格式化的会话请求字符串
        """
        try:
            # 解析命令
            input_text, prompt_steps = self.parse_pipeline_command(command)
            
            # 检查是否有保存指令
            save_file = False
            actual_prompts = []
            
            for step in prompt_steps:
                if step.lower() in ['保存文件', '保存', 'save', 'save file']:
                    save_file = True
                else:
                    actual_prompts.append(step)
            
            if not actual_prompts:
                return "❌ 错误：没有找到有效的prompt步骤"
            
            # 生成会话请求
            separator = "=" * 60
            request_parts = []
            
            # 添加标题
            request_parts.append(f"""
{separator}
🚀 PROMPT PIPELINE 会话请求
{separator}

📊 **输入文本**：{input_text}
🔗 **处理链**：{' → '.join(actual_prompts)}
💾 **保存文件**：{'是' if save_file else '否'}

{separator}
""")
            
            # 为每个prompt生成处理步骤
            current_input = input_text
            
            for i, prompt_name in enumerate(actual_prompts, 1):
                # 查找prompt文件
                prompt_file = self.find_prompt_file(prompt_name)
                if not prompt_file:
                    return f"❌ 错误：找不到prompt文件 '{prompt_name}'"
                
                # 加载prompt内容
                prompt_content = self.load_prompt_content(prompt_file)
                
                # 生成步骤请求
                step_request = f"""
### 🔄 步骤 {i}/{len(actual_prompts)}: {prompt_name}

**Prompt文件**: `{prompt_file.relative_to(self.prompt_dir)}`

**Prompt内容**:
```
{prompt_content}
```

**当前输入**:
```
{current_input}
```

**请求**: 请根据上述prompt处理当前输入，直接输出处理结果。

---
"""
                request_parts.append(step_request)
                
                # 为下一步准备（如果有多个步骤）
                if i < len(actual_prompts):
                    current_input = f"[步骤{i}的输出结果]"
            
            # 添加保存说明
            if save_file:
                request_parts.append(f"""
### 💾 保存说明

处理完成后，最终结果将保存到：
- 目录：`prompt/output/{datetime.now().strftime('%Y-%m-%d')}/`
- 文件名：`HHMMSS_{actual_prompts[-1]}.md`

{separator}
""")
            
            return ''.join(request_parts)
            
        except Exception as e:
            return f"❌ 生成请求出错：{e}"


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="Prompt Session - 会话内直接处理版本",
        epilog="示例: python prompt_session.py \"你的文字 |> 文章改写 |> 极简侘寂\""
    )
    parser.add_argument(
        "command",
        help="管道命令，格式：'输入文字 |> prompt1 |> prompt2 |> ...'",
        nargs='?'
    )
    parser.add_argument(
        "--list-prompts",
        action="store_true",
        help="列出所有可用的prompt"
    )
    
    args = parser.parse_args()
    
    session = PromptSession()
    
    if args.list_prompts:
        print("📋 可用的Prompt列表：")
        print("-" * 40)
        for md_file in session.prompt_dir.rglob("*.md"):
            if md_file.name == "README.md" or "output" in md_file.parts:
                continue
            relative_path = md_file.relative_to(session.prompt_dir)
            prompt_name = str(relative_path).replace('.md', '')
            print(f"  • {prompt_name}")
            print(f"    文件：{relative_path}")
            print()
        return
    
    if not args.command:
        print("❌ 错误：请提供管道命令")
        print("使用 --help 查看帮助信息")
        print("使用 --list-prompts 查看可用的prompt")
        return
    
    request = session.generate_session_request(args.command)
    print(request)


if __name__ == "__main__":
    main()
