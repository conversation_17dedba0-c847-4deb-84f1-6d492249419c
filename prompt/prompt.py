import sys
import os
import asyncio
from pathlib import Path
from datetime import datetime
import argparse
from typing import Optional

async def call_llm(prompt_content: str, input_text: str, response_file: Optional[str] = None, simulate: bool = False) -> str:
    """
    调用大语言模型 (LLM) 的函数。
    默认行为是交互式的：打印请求，等待用户从 stdin 粘贴 LLM 的响应。
    - `response_file` 模式: 从文件加载响应，优先级最高。
    - `simulate` 模式: 生成一个通用的、内容无关的模拟响应。
    """
    clean_input = input_text.strip()

    if response_file:
        print(f"\n[INFO] Loading LLM response from file: {response_file}")
        try:
            with open(response_file, 'r', encoding='utf-8') as f:
                actual_llm_response = f.read()
        except FileNotFoundError:
            print(f"[ERROR] Response file not found: {response_file}")
            actual_llm_response = "(Error: Response file not found.)"
    elif simulate:
        print("\n[INFO] Simulating generic LLM call...")
        actual_llm_response = f"(This is a simulated response for the input: '{clean_input}'.)"
    else: # 默认的交互模式
        print("\n" + "="*20 + " AI CALL REQUEST " + "="*20)
        print(f"--- PROMPT ---\n{prompt_content}")
        print(f"\n--- INPUT ---\n{clean_input}")
        print("="*59)
        print("\n>>> Please provide the AI's response below. Press Ctrl+D (Mac/Linux) or Ctrl+Z+Enter (Windows) when done:")
        actual_llm_response = sys.stdin.read()
        print("\n[INFO] Response received from user.")

    output_structure = f"""--- INPUT ---
{clean_input}

--- OUTPUT ---
{actual_llm_response.strip()}
"""
    return output_structure

async def main():
    """主执行函数"""
    parser = argparse.ArgumentParser(
        description="一个基于管道的 Prompt 工作流引擎。",
        epilog='示例: echo "输入文本" | python prompt/prompt.py rewrite/文章改写 --save'
    )
    parser.add_argument(
        "prompts",
        nargs='+',
        help="一个或多个按顺序执行的 Prompt 文件名 (无需 .md 后缀)。"
    )
    parser.add_argument(
        "--save",
        action="store_true",
        help="如果提供此标志，则将输出保存到文件中，而不是打印到标准输出。"
    )
    parser.add_argument(
        '--llm-response-from-file',
        type=str,
        default=None,
        help='Path to a file to use as the LLM response, for testing or injection.'
    )
    parser.add_argument(
        '--simulate',
        action='store_true',
        help='Use a generic simulated response instead of the interactive mode.'
    )
    args = parser.parse_args()

    # 1. 检查是否有管道输入
    if sys.stdin.isatty():
        print("[ERROR] 此脚本需要从管道接收输入。", file=sys.stderr)
        print("用法示例: echo \"你的输入文本\" | python prompt/prompt.py your_prompt", file=sys.stderr)
        sys.exit(1)

    # 2. 读取标准输入
    current_content = sys.stdin.read()
    print(f"[INFO] Executing workflow: {' -> '.join(args.prompts)}")

    # 3. 按顺序执行 Prompt 链
    script_dir = Path(__file__).parent.resolve()
    for prompt_name in args.prompts:
        prompt_path = script_dir / f"{prompt_name}.md"
        print(f"[INFO] Applying prompt: {prompt_path}")
        try:
            prompt_content = prompt_path.read_text(encoding='utf-8')
            current_content = await call_llm(prompt_content, current_content, args.llm_response_from_file, args.simulate)
        except Exception as e:
            print(f"[ERROR] Failed to read or execute prompt: {prompt_path}\n{e}", file=sys.stderr)
            sys.exit(1)

    # 4. 输出结果 (标准输出或文件)
    if args.save:
        # 保存到文件
        now = datetime.now()
        today_str = now.strftime("%Y-%m-%d")
        time_stamp = now.strftime("%H%M%S")
        output_dir = script_dir / 'output' / today_str
        last_prompt_name = args.prompts[-1].replace('/', '_').replace('\\', '_')
        output_filename = f"{time_stamp}_{last_prompt_name}.md"
        output_path = output_dir / output_filename

        try:
            output_dir.mkdir(parents=True, exist_ok=True)
            output_path.write_text(current_content, encoding='utf-8')
            print("\n[SUCCESS] Workflow finished!")
            print(f"Output saved to: {output_path}", file=sys.stderr)
        except Exception as e:
            print(f"[ERROR] Failed to write output file: {output_path}\n{e}", file=sys.stderr)
            sys.exit(1)
    else:
        # 打印到标准输出
        print(current_content, end='')

if __name__ == "__main__":
    asyncio.run(main())
