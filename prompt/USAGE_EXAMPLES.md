# Prompt Pipeline 使用示例

这个文档提供了详细的使用示例，帮助你快速上手 Prompt Pipeline 系统。

## 基础使用

### 1. 查看可用的Prompt

```bash
python prompt/prompt_pipeline.py --list-prompts
```

输出示例：
```
📋 可用的Prompt列表：
----------------------------------------
  • rewrite/多维重构
    文件：rewrite/多维重构.md

  • rewrite/文章改写
    文件：rewrite/文章改写.md

  • web/极简侘寂
    文件：web/极简侘寂.md

  • web/Dia 网站
    文件：web/Dia 网站.md
```

### 2. 单个Prompt处理

```bash
python prompt/prompt_pipeline.py "这是一段需要改写的原始文本 |> 文章改写"
```

系统会：
1. 显示处理概览
2. 加载 `rewrite/文章改写.md` prompt文件
3. 格式化显示prompt内容和输入文本
4. 等待你将请求发送给AI并粘贴响应

### 3. 多Prompt串联处理

```bash
python prompt/prompt_pipeline.py "Ship30创始人的创业故事 |> 文章改写 |> 极简侘寂"
```

处理流程：
1. **步骤1**：使用"文章改写"prompt处理原始文本
2. **步骤2**：将步骤1的输出作为输入，使用"极简侘寂"prompt进行网页设计

### 4. 保存处理结果

```bash
python prompt/prompt_pipeline.py "原始内容 |> 文章改写 |> 极简侘寂 |> 保存文件"
```

添加 `|> 保存文件` 后，最终结果会自动保存到 `output/YYYY-MM-DD/` 目录。

## 高级使用技巧

### 智能Prompt匹配

系统支持多种prompt名称格式：

```bash
# 完整路径
python prompt/prompt_pipeline.py "文本 |> rewrite/文章改写"

# 简短名称（自动匹配）
python prompt/prompt_pipeline.py "文本 |> 文章改写"

# 混合使用
python prompt/prompt_pipeline.py "文本 |> 文章改写 |> web/极简侘寂"
```

### 复杂工作流示例

#### 示例1：内容创作工作流

```bash
python prompt/prompt_pipeline.py "我想分享一个关于时间管理的个人经验 |> 文章改写 |> 保存文件"
```

这个工作流会：
1. 将个人经验改写成有吸引力的文章
2. 保存到今天的output目录

#### 示例2：网站内容生成工作流

```bash
python prompt/prompt_pipeline.py "产品介绍：我们的AI写作助手 |> 文章改写 |> 极简侘寂 |> 保存文件"
```

这个工作流会：
1. 将产品介绍改写成吸引人的文案
2. 转换成极简侘寂风格的网页
3. 保存完整的HTML文件

## 实际操作流程

### 完整操作示例

假设你要处理这个命令：
```bash
python prompt/prompt_pipeline.py "学习编程的心得体会 |> 文章改写 |> 保存文件"
```

**步骤1：系统显示概览**
```
🚀 启动 Prompt Pipeline
📊 输入文本：学习编程的心得体会
🔗 处理链：文章改写
💾 保存文件：是

开始处理...
```

**步骤2：处理第一个prompt**
```
🔄 处理步骤 1/1: 文章改写
📁 使用prompt文件：rewrite/文章改写.md

============================================================
🔄 PROMPT PIPELINE - 步骤 1/1
============================================================

📋 PROMPT 内容：
[显示完整的prompt内容]

📝 输入文本：
学习编程的心得体会

============================================================
请根据上述prompt处理输入文本，直接输出处理结果：
```

**步骤3：你的操作**
1. 复制上述格式化的请求
2. 发送给AI（比如当前的会话）
3. 复制AI的响应
4. 粘贴到终端并按 Ctrl+D

**步骤4：系统完成处理**
```
✅ 步骤 1 完成

🎉 Pipeline 执行完成！

💾 文件已保存到：/path/to/prompt/output/2025-06-17/143052_文章改写.md
📁 相对路径：output/2025-06-17/143052_文章改写.md

最终输出内容：
----------------------------------------
[AI处理后的最终内容]
----------------------------------------
```

## 常见问题

### Q: 如何创建新的Prompt？

A: 在相应的分类目录下创建 `.md` 文件即可：

```bash
# 创建新的翻译类prompt
mkdir -p prompt/translate
echo "你的prompt内容" > prompt/translate/中英翻译.md

# 立即可用
python prompt/prompt_pipeline.py "Hello World |> 中英翻译"
```

### Q: 如何处理包含特殊字符的文本？

A: 使用引号包围整个命令：

```bash
python prompt/prompt_pipeline.py "包含|特殊>字符的文本 |> 文章改写"
```

### Q: 可以跳过某个步骤吗？

A: 目前不支持跳过，但你可以：
1. 分别执行不同的命令
2. 或者创建新的prompt组合

### Q: 输出文件的命名规则是什么？

A: 格式为 `HHMMSS_最后一个prompt名称.md`
- `HHMMSS`：执行时间（时分秒）
- `最后一个prompt名称`：处理链中的最后一个prompt
- 保存在 `output/YYYY-MM-DD/` 目录下

## 最佳实践

1. **先测试单个prompt**：在串联之前，先单独测试每个prompt
2. **使用有意义的输入**：提供足够详细的输入文本，获得更好的处理效果
3. **保存重要结果**：对于满意的输出，记得添加 `|> 保存文件`
4. **组织prompt文件**：按功能分类组织prompt，便于管理和查找
5. **版本控制**：将prompt文件纳入版本控制，跟踪变更历史

## 扩展建议

### 创建专用的prompt组合

你可以创建专门的prompt来实现常用的组合：

```markdown
<!-- prompt/workflow/博客发布.md -->
## 系统提示词

这是一个组合prompt，请按以下步骤处理输入文本：

1. 首先使用"文章改写"的方法优化内容
2. 然后转换为适合博客发布的格式
3. 添加适当的标题和标签建议

请直接输出最终的博客文章格式。
```

然后使用：
```bash
python prompt/prompt_pipeline.py "我的想法 |> 博客发布 |> 保存文件"
```

这样可以将复杂的工作流封装成单个prompt，提高效率。
