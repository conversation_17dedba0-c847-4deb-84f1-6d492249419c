#!/usr/bin/env python3
"""
简化版本的 Prompt Pipeline 测试

用于验证修复后的输入处理逻辑
"""

import sys
from pathlib import Path

def test_input_processing():
    """测试输入处理逻辑"""
    print("🧪 测试输入处理...")
    print("请输入一些文本，输入 '---END---' 结束：")
    
    response_lines = []
    while True:
        try:
            line = input()
            if line.strip() == '---END---':
                break
            response_lines.append(line)
        except EOFError:
            # 遇到EOF（Ctrl+D），结束输入
            break
    
    result = '\n'.join(response_lines).strip()
    print(f"\n收到的内容：")
    print("=" * 40)
    print(result)
    print("=" * 40)
    print(f"总共 {len(response_lines)} 行")

if __name__ == "__main__":
    test_input_processing()
