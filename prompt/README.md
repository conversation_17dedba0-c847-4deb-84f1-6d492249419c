# Prompt-Based Workflow Engine

这是一个基于文件系统的、可扩展的 Prompt 工作流引擎，旨在将内容处理任务自动化和规范化。

## 核心里念

- **Prompt 即文件**：每一个内容处理任务（如文章改写、风格转换、代码生成）都由一个或多个 Prompt 文件定义。
- **工作流驱动**：通过简单的命令，可以执行单个 Prompt 或将多个 Prompt 串联成一个强大的处理流水线。
- **本地化与可扩展**：所有 Prompt 和输出都存储在本地，方便管理和版本控制。通过添加新的 `.md` 文件即可轻松扩展新功能。

## 目录结构

```
prompt/
├── README.md           # 本说明文档
├── prompt.py           # 工作流执行脚本
├── output/             # 所有任务的输出目录
│   └── YYYY-MM-DD/     # 按日期归档的输出结果
│       └── ...
├── rewrite/            # “改写”类 Prompt
│   ├── 文章改写.md
│   └── 多维重构.md
└── web/                # “Web开发”类 Prompt
    └── ...
```

- `prompt/`: 工作流引擎的根目录。
- `prompt/<category>/`: 用于存放某一类别的 Prompt 文件，例如 `rewrite`、`summary`、`translate` 等。
- `prompt/output/`: 所有生成内容的存放位置。
- `prompt/output/YYYY-MM-DD/`: 为了方便追溯，输出文件会按执行日期进行归档。

## 简介

这是一个灵活的、基于命令行的 Prompt 工作流引擎。它允许你将一系列的 Prompt 如同 Unix 管道一样串联起来，前一个 Prompt 的输出会成为下一个 Prompt 的输入。目前，它已集成了 Google 的 Gemini 1.5 Pro 模型，可以将简单的输入文本“炼金”成高质量的输出。

## 环境设置

在开始之前，你需要进行一些简单的设置。

### 1. 安装依赖

本脚本需要 `google-generativeai` 库来调用 Gemini API。请在 `prompt` 目录下运行以下命令来安装它：

```bash
pip install -r requirements.txt
```

### 2. 配置 API 密钥

为了使用 Gemini 模型，你需要一个 Google API 密钥。请将你的密钥设置为一个名为 `GOOGLE_API_KEY` 的环境变量。

在你的 shell 配置文件中 (例如 `~/.zshrc`, `~/.bash_profile`) 添加下面这行：

```bash
export GOOGLE_API_KEY='你的_API_密钥_放在这里'
```

**重要提示**: 修改完配置文件后，请务必重新加载它 (`source ~/.zshrc`) 或重启你的终端，以使环境变量生效。

## 如何创建新 Prompt

在相应的分类目录下（如 `prompt/rewrite/`），直接创建一个新的 Markdown 文件（`.md`）即可。文件名即为 Prompt 的唯一标识符。

例如，要创建一个“将文本翻译为英文”的 Prompt，可以创建文件 `prompt/translate/to_english.md`。

## 如何执行工作流

我们的工作流引擎 `prompt.py` 被设计为遵循 Unix 哲学，通过标准输入/输出（stdin/stdout）和管道（`|`）进行数据流处理，使其可以轻松地与其他命令行工具组合。

### 命令格式

```bash
<输入源> | python prompt/prompt.py <prompt_1> <prompt_2> ... [--save]
```

-   **`<输入源>`**: 任何能够产生文本输出到 stdout 的命令，例如 `cat a.txt` 或 `echo "some text"`。
-   **`<prompt_n>`**: 一个或多个按顺序执行的 Prompt 文件名（位于 `prompt/` 目录下，无需 `.md` 后缀）。
-   **`--save`** (可选): 一个标志。如果提供，脚本会将最终结果保存到 `output/` 目录下的文件中，而不是打印到屏幕。执行信息（如进度、成功或错误消息）会打印到标准错误流（stderr），不会干扰管道。

### 示例

1.  **单个 Prompt (输出到屏幕)**

    ```bash
    echo "这是一段需要被改写的原始文本。" | python prompt/prompt.py rewrite/文章改写
    ```

2.  **链式 Prompt (从文件输入，输出到屏幕)**

    ```bash
    cat prompt/test_article.md | python prompt/prompt.py rewrite/文章改写 rewrite/多维重构
    ```

3.  **链式 Prompt (保存到文件)**

    ```bash
    cat prompt/test_article.md | python prompt/prompt.py rewrite/文章改写 rewrite/多维重构 --save
    ```
    在这种情况下，最终的文本内容会保存到 `output/YYYY-MM-DD/` 目录中，而屏幕上只会显示执行状态信息。

## 输入与输出

-   **主要输入**: 始终来自标准输入 (stdin)。
-   **主要输出**: 默认是标准输出 (stdout)，以便于管道连接。如果使用 `--save` 标志，主要输出会重定向到文件。
-   **日志/状态输出**: 始终发送到标准错误 (stderr)，以避免污染数据流。

## 扩展

你可以自由创建新的 `.md` 文件并将其放入 `prompt/` 下的任何子目录中，然后就可以在命令行中像调用函数一样调用它。例如，创建一个 `prompt/summarize/会议纪要.md`，然后就可以这样使用：

```bash
cat meeting_notes.txt | python prompt/prompt.py summarize/会议纪要
```
